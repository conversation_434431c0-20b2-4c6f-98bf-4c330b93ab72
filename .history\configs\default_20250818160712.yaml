sync:
  enable: true
  max_sync_dt_ms: 30
  pose_required: true

visibility:
  method_primary: zbuffer
  method_fallback: rays
  tau_base_m: 0.2  # REDUCED: More precise depth tolerance
  tau_scale_per_m: 0.015  # REDUCED: More precise depth tolerance
  min_pixels_for_zbuffer: 100  # REDUCED: Lower threshold to process more objects
  min_depth_hits_for_zbuffer: 30  # REDUCED: Lower threshold to process more objects
  per_view_visible_thresh: 0.10  # REDUCED: Lower threshold for visibility
  include_noFOV_as_zero: false
  adaptive_tolerance: true  # NEW: Enable adaptive tolerance based on distance
  quality_threshold: 0.3  # NEW: Minimum quality score for results

fov_filter:
  enabled: true
  use_corner_check: false
  vfov_deg_default: 50.0  # INCREASED: More permissive vertical FOV
  debug_filtering: true  # NEW: Enable debug logging for filtering
  fallback_on_filter: true  # NEW: Allow fallback when filtering is too aggressive

camera_fovs_deg:
  60_front:   {h: 60.0,  v: 40.0}
  120_front:  {h: 120.0, v: 40.0}
  120_back:   {h: 120.0, v: 40.0}
  120_left:   {h: 120.0, v: 40.0}
  120_right:  {h: 120.0, v: 40.0}
  left_back:  {h: 120.0, v: 40.0}
  right_back: {h: 120.0, v: 40.0}

ego_sector_map_deg:
  60_front:   {center:   0.0, half: 30.0}
  120_front:  {center:   0.0, half: 60.0}
  120_left:   {center:  90.0, half: 60.0}
  120_right:  {center: -90.0, half: 60.0}
  120_back:   {center: 180.0, half: 60.0}
  left_back:  {center: 150.0, half: 60.0}
  right_back: {center: -150.0, half: 60.0}

training_filter:
  drop_if_level_ge: 4
  min_vis_avg: 0.5
  loss_weight_by_confidence: true

camera_map:
  60_front: H60L-E08160504
  120_front: H120L-E06170513
  120_back: H120L-E06170599
  120_left: H120L-F05160609
  120_right: H120L-E12190550
  left_back: H120L-P05160612
  right_back: H120L-F05160614
