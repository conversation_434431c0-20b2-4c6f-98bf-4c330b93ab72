import numpy as np
import logging

# Set up logging for debugging visibility calculations
logger = logging.getLogger(__name__)


def visibility_via_zbuffer(box3d, cam, depth_img: np.ndarray, tau_base=0.3, tau_scale=0.02):
    """Compute visible pixel ratio inside projected box mask by comparing depth.

    CORRECTED: Fixed depth comparison logic to properly identify visible surfaces.
    Object is visible if scene depth is close to object depth within tolerance.

    Returns: (ratio or 0.0, stats dict) where stats includes samples, hits, avg_z, hit_rate.
    """
    H, W = depth_img.shape
    pts = box3d['surface_pts']  # precomputed Nx3
    if pts.size == 0:
        return 0.0, {"samples": 0, "hits": 0, "hit_rate": 0.0}
    uvz = cam.project(pts)
    u = np.round(uvz[:,0]).astype(int)
    v = np.round(uvz[:,1]).astype(int)
    z = uvz[:,2]
    in_img = np.isfinite(z) & (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[in_img], v[in_img], z[in_img]
    if len(u) == 0:
        return 0.0, {"samples": 0, "hits": 0, "hit_rate": 0.0}
    D = depth_img[v, u]
    tau = np.maximum(tau_base, tau_scale * z)
    finite = np.isfinite(D)

    # CRITICAL FIX: Corrected depth comparison logic
    # Object is visible if scene depth is close to object depth (within tolerance)
    # Previous: (z - D) <= tau  [INCORRECT - would mark occluded objects as visible]
    # Corrected: abs(D - z) <= tau  [CORRECT - checks if depths are approximately equal]
    visible = finite & (np.abs(D - z) <= tau)

    samples = int(len(visible))
    hits = int(finite.sum())
    hit_rate = float(hits) / float(max(1, samples))
    ratio = float(visible.sum()) / float(samples)

    # Add debug logging for visibility calculation
    logger.debug(f"Visibility calculation: samples={samples}, hits={hits}, visible={visible.sum()}, ratio={ratio:.3f}")

    stats = {"samples": samples, "hits": hits, "hit_rate": hit_rate, "avg_z": float(np.mean(z))}
    return ratio, stats
