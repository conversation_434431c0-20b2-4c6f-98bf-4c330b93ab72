import argparse
from pathlib import Path
import json
import yaml
import numpy as np
import cv2
from ..io.dataset_loader import ClipDataset
from ..io.calib_parser import parse_calibrated_sensor_pb_txt
from ..io.pose_loader import PoseStream
from ..calib.camera_model import Camera
from ..calib.transforms import transform_points, inv_T
from ..geometry.depth_tools import pointcloud_to_depth
from ..geometry.box3d import sample_box_surface, sample_box_surface_adaptive, box_corners
from ..visibility.per_camera_pixel_ratio import visibility_via_zbuffer
from ..visibility.surface_ray_sampling import visibility_via_ray_sampling
from ..visibility.fusion import fuse_camera_visibility, to_occlusion_level
from ..visibility.diagnostics import visibility_confidence
from ..visibility.sync_compensation import compensate_points_to_time
from ..visibility.fov_filter import ego_sector_gate, cam_fov_gate_point, cam_fov_gate_corners
from ..vis.draw2d import draw_projected_points, draw_box_projections, overlay_visibility_score, make_mosaic
from ..vis.exporters import save_image

# End-to-end batch with projection visualization and sync fields


def load_pcd_xyz(pcd_path: Path) -> np.ndarray:
    """Load XYZ coordinates from PCD file, handling both plain text and PCD format."""
    try:
        # First try loading as plain text (for backward compatibility)
        pts = np.loadtxt(str(pcd_path), usecols=(0,1,2))
        if pts.ndim == 1:
            pts = pts.reshape(1, -1)
        return pts.astype(float)
    except Exception as e:
        # If that fails, try parsing as PCD format
        try:
            with open(pcd_path, 'r') as f:
                lines = f.readlines()

            # Find the DATA line to determine where the actual data starts
            data_start_idx = None
            for i, line in enumerate(lines):
                if line.strip().startswith('DATA'):
                    data_start_idx = i + 1
                    break

            if data_start_idx is None:
                print(f">>> DEBUG: Could not find DATA section in PCD file {pcd_path}")
                return np.empty((0,3), dtype=float)

            # Load data starting from the DATA line, taking only first 3 columns (x, y, z)
            data_lines = lines[data_start_idx:]
            pts = []
            for line in data_lines:
                line = line.strip()
                if line:  # Skip empty lines
                    parts = line.split()
                    if len(parts) >= 3:  # Ensure we have at least x, y, z
                        try:
                            x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                            pts.append([x, y, z])
                        except ValueError:
                            continue  # Skip malformed lines

            if not pts:
                return np.empty((0,3), dtype=float)

            return np.array(pts, dtype=float)

        except Exception as e2:
            print(f">>> DEBUG: Failed to load PCD file {pcd_path} as both plain text and PCD format: {e}, {e2}")
            return np.empty((0,3), dtype=float)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--clip_dir', required=True)
    parser.add_argument('--config', default=str(Path(__file__).resolve().parents[2] / 'configs' / 'default.yaml'))
    parser.add_argument('--save_dir', default='./outputs')
    parser.add_argument('--viz_cam', action='store_true')
    parser.add_argument('--method', default='zbuffer', choices=['zbuffer','rays','auto'])
    parser.add_argument('--samples', type=int, default=2048)
    args = parser.parse_args()

    cfg = yaml.safe_load(open(args.config, 'r'))
    clip = ClipDataset(args.clip_dir)
    ts_list = clip.list_timestamps()

    calibs = parse_calibrated_sensor_pb_txt(str(clip.calib_pb_txt))
    pose = PoseStream(str(clip.pose_txt))

    cam_map = cfg.get('camera_map', {})
    cams = {}
    for dir_name, sensor_name in cam_map.items():
        if sensor_name not in calibs.cams:
            continue
        c = calibs.cams[sensor_name]
        cams[dir_name] = Camera(
            name=dir_name,
            K=c['K'],
            dist=c['dist'],
            width=c['width'],
            height=c['height'],
            T_base_cam=c['T_base_cam'],
        )

    save_root = Path(args.save_dir) / Path(args.clip_dir).name
    save_root.mkdir(parents=True, exist_ok=True)

    for ts in ts_list:
        t_lidar = float(ts)
        paths = clip.frame_paths_by_ts(ts)
        pts = load_pcd_xyz(paths['pcd'])
        j = json.loads(Path(paths['json']).read_text(encoding='utf-8'))
        objects = j.get('result', {}).get('data', [])

        # Depth maps per camera with time compensation to each image's timestamp
        depth_imgs = {}
        cam_dt = {}
        images_cache = {}
        comp_pts_per_cam = {}



        for cam_name, cam in cams.items():
            img_path = paths['images'][cam_name]
            if not img_path.exists():
                continue
            try:
                t_img = float(img_path.stem)
            except Exception:
                t_img = t_lidar
            dt = abs(t_img - t_lidar)
            cam_dt[cam_name] = dt
            # compensate points from lidar time to image time
            pts_cam_time = compensate_points_to_time(pose, pts, t_lidar, t_img)
            comp_pts_per_cam[cam_name] = pts_cam_time
            H, W = cam.height, cam.width
            D, hits, uvz_hits = pointcloud_to_depth(pts_cam_time, cam, H, W)
            depth_imgs[cam_name] = (D, hits, uvz_hits)



            if args.viz_cam:
                img = cv2.imread(str(img_path))
                if img is not None:
                    images_cache[cam_name] = img

        per_object_aug = {}
        # Prepare per-camera images for drawing once and overlay multiple objects
        drawn_images = {k: v.copy() for k, v in images_cache.items()}

        for obj in objects:
            center = obj['3Dcenter']
            size = obj['3Dsize']
            cx, cy, cz = center['x'], center['y'], center['z']
            l, w, h = size['length'], size['width'], size['height']
            yaw = size.get('rz', size.get('alpha', 0.0))
            surf = sample_box_surface((cx,cy,cz), l, w, h, yaw, samples_per_face=800)
            corners = box_corners((cx,cy,cz), l, w, h, yaw)
            box = {'surface_pts': surf}

            per_cam = {}
            stats_total = {"samples": 0, "hits": 0}
            fov_cfg = cfg.get('fov_filter', {}).get('enabled', True)
            use_corner = cfg.get('fov_filter', {}).get('use_corner_check', False)
            # prepare FOV config maps
            cam_fovs_deg = cfg.get('camera_fovs_deg', {})
            cam_fov_rad = {k: {"h": v.get('h', 120.0)*np.pi/180.0, "v": v.get('v', cfg.get('fov_filter',{}).get('vfov_deg_default',40.0))*np.pi/180.0} for k,v in cam_fovs_deg.items()}
            sector_map = cfg.get('ego_sector_map_deg', {})
            sectors = {k: {"center": v['center']*np.pi/180.0, "half": v['half']*np.pi/180.0} for k,v in sector_map.items()}

            for cam_name, cam in cams.items():
                # FOV gating
                gate_ok = True
                gate_reason = "ok"
                if fov_cfg:
                    ok1, r1 = ego_sector_gate((cx,cy,cz), cam_name, sectors)
                    if not ok1:
                        gate_ok, gate_reason = False, f"ego_sector:{r1}"
                    else:
                        if use_corner:
                            ok2, r2 = cam_fov_gate_corners(corners, cam.T_base_cam, cam_name, cam_fov_rad)
                        else:
                            ok2, r2 = cam_fov_gate_point((cx,cy,cz), cam.T_base_cam, cam_name, cam_fov_rad)
                        if not ok2:
                            gate_ok, gate_reason = False, f"cam_fov:{r2}"
                if not gate_ok:
                    per_cam[cam_name] = None
                    continue

                D, hits, uvz_hits = depth_imgs.get(cam_name, (None, None, None))
                if D is None:
                    per_cam[cam_name] = None
                    continue

                # If depth is too sparse near the object, return None to avoid misleading 0.0
                min_hits = cfg['visibility'].get('min_depth_hits_for_zbuffer', 50)
                if hits < min_hits:
                    per_cam[cam_name] = None
                    continue

                if args.method == 'zbuffer':
                    r, st = visibility_via_zbuffer(box, cam, D, tau_base=cfg['visibility']['tau_base_m'], tau_scale=cfg['visibility']['tau_scale_per_m'])
                elif args.method == 'rays':
                    r, st = visibility_via_ray_sampling(box, cam, D, samples=args.samples)
                else:
                    r, st = visibility_via_zbuffer(box, cam, D, tau_base=cfg['visibility']['tau_base_m'], tau_scale=cfg['visibility']['tau_scale_per_m'])
                    if st.get('samples', 0) < cfg['visibility']['min_pixels_for_zbuffer']:
                        r, st = visibility_via_ray_sampling(box, cam, D, samples=args.samples)
                per_cam[cam_name] = r
                stats_total['samples'] += st.get('samples', 0)
                stats_total['hits'] += st.get('hits', 0)

                # Visualization overlay (draw on cached image for this camera)
                if args.viz_cam and cam_name in drawn_images:
                    img = drawn_images[cam_name]
                    draw_projected_points(img, cam, comp_pts_per_cam.get(cam_name, pts))
                    draw_box_projections(img, cam, corners)
                    overlay_visibility_score(img, r)

            vis_avg, vis_max, visible_views = fuse_camera_visibility(per_cam, include_noFOV_as_zero=cfg['visibility']['include_noFOV_as_zero'])
            level = to_occlusion_level(vis_avg)
            conf = visibility_confidence(stats_total['samples'], stats_total['hits'], sync_dt=0.0)

            obj_aug = {
                "visibility": {
                    "per_camera": per_cam,
                    "visibility_rate_avg": vis_avg,
                    "visibility_rate_max": vis_max,
                    "occlusion_level": level,
                    "visible_in_views": visible_views,
                    "visibility_method": args.method,
                    "visibility_confidence": conf
                },
                "sync": {
                    "compensated": True,
                    "sync_dt_sec": {cam: float(cam_dt.get(cam, 0.0)) for cam in cams.keys()},
                    "compensation_method": "ego_pose_interpolation_SE3"
                }
            }
            per_object_aug[obj['ObjectID']] = obj_aug

        out_json = save_root / f"{ts}.json"
        from .integrate_json import write_augmented_json
        write_augmented_json(str(paths['json']), str(out_json), per_object_aug)

        # Save per-camera images for this ts
        if args.viz_cam and drawn_images:
            for cam_name, img in drawn_images.items():
                out_img = save_root / 'viz' / cam_name / f"{ts}.jpg"
                save_image(img, str(out_img))
            # optional mosaic across cameras
            from ..vis.draw2d import make_mosaic
            imgs_in_order = [drawn_images[k] for k in sorted(drawn_images.keys())]
            mosaic = make_mosaic(imgs_in_order, cols=4)
            if mosaic is not None:
                save_image(mosaic, str(save_root / 'viz' / f"{ts}_mosaic.jpg"))

    print(f"Done: outputs saved to {save_root}")

if __name__ == '__main__':
    main()
