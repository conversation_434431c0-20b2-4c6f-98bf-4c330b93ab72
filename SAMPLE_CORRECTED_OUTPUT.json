{"data": {"3d_url": "1733374539.701036214.pcd"}, "nCloud": 424559, "result": {"data": [{"3Dcenter": {"x": 29.538637542231694, "y": -5.306071928656523, "z": 0.7824107308350441}, "3Dsize": {"height": 1.5111111111111113, "length": 4.4, "width": 1.9524412582893789, "alpha": -0.06981317007977321, "rx": 0, "ry": 0, "rz": -0.06981317007977321}, "group": "0", "ObjectID": 1, "label": "5000", "sublabel": "5003", "pointnum": 432, "is_excessive_layering": "False", "visibility": {"per_camera": {"60_front": 0.006590136054421769, "120_front": 0.10884353741496598, "120_back": null, "120_left": null, "120_right": null, "left_back": null, "right_back": null}, "visibility_rate_avg": 0.057716836734693876, "visibility_rate_max": 0.10884353741496598, "occlusion_level": 4, "visible_in_views": [], "visibility_method": "<PERSON><PERSON><PERSON>", "visibility_confidence": 0.5379464285714286, "continuous_score": 0.057716836734693876, "reliability_weight": 0.5379464285714286, "perspective_to_bev_weight": 0.6234567890123456, "voxel_visibility_map": {"voxel_size": 0.2, "voxel_count": 1247, "avg_voxel_visibility": 0.057716836734693876, "bbox_dimensions": [4.4, 1.9524412582893789, 1.5111111111111113], "implementation": "placeholder"}, "occupancy_confidence": 0.7123456789012345, "spatial_uncertainty": 0.2456789012345679, "sample_density": 2.345678901234568, "depth_hit_rate": 0.6666666666666666, "geometric_consistency": 0.8901234567890123}, "sync": {"compensated": true, "sync_dt_sec": {"60_front": 0.0, "120_front": 0.0, "120_back": 0.0, "120_left": 0.0, "120_right": 0.0, "left_back": 0.0, "right_back": 0.0}, "compensation_method": "ego_pose_interpolation_SE3"}}, {"3Dcenter": {"x": 15.234567890123456, "y": 2.345678901234568, "z": 1.2345678901234567}, "3Dsize": {"height": 1.8, "length": 4.2, "width": 1.8, "alpha": 0.12345678901234566, "rx": 0, "ry": 0, "rz": 0.12345678901234566}, "group": "0", "ObjectID": 2, "label": "5000", "sublabel": "5003", "pointnum": 856, "is_excessive_layering": "False", "visibility": {"per_camera": {"60_front": 0.8234567890123456, "120_front": 0.9123456789012345, "120_back": null, "120_left": 0.4567890123456789, "120_right": 0.3456789012345678, "left_back": null, "right_back": null}, "visibility_rate_avg": 0.6345678901234567, "visibility_rate_max": 0.9123456789012345, "occlusion_level": 2, "visible_in_views": ["60_front", "120_front", "120_left"], "visibility_method": "<PERSON><PERSON><PERSON>", "visibility_confidence": 0.8765432109876543, "continuous_score": 0.6345678901234567, "reliability_weight": 0.8765432109876543, "perspective_to_bev_weight": 0.7890123456789012, "voxel_visibility_map": {"voxel_size": 0.2, "voxel_count": 1890, "avg_voxel_visibility": 0.6345678901234567, "bbox_dimensions": [4.2, 1.8, 1.8], "implementation": "placeholder"}, "occupancy_confidence": 0.8901234567890123, "spatial_uncertainty": 0.12345678901234566, "sample_density": 4.567890123456789, "depth_hit_rate": 0.8765432109876543, "geometric_consistency": 0.9456789012345678}, "sync": {"compensated": true, "sync_dt_sec": {"60_front": 0.0, "120_front": 0.0, "120_back": 0.0, "120_left": 0.0, "120_right": 0.0, "left_back": 0.0, "right_back": 0.0}, "compensation_method": "ego_pose_interpolation_SE3"}}]}}