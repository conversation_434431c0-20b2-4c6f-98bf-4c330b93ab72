from typing import Dict, <PERSON><PERSON>, List
import numpy as np
import logging

logger = logging.getLogger(__name__)

def fuse_camera_visibility(per_cam: Dict[str, float], include_noFOV_as_zero=False) -> Tuple[float, float, List[str]]:
    vals = []
    visible_views = []
    for cam, r in per_cam.items():
        if r is None:
            if include_noFOV_as_zero:
                vals.append(0.0)
            continue
        vals.append(r)
        if r >= 0.15:
            visible_views.append(cam)
    if not vals:
        return 0.0, 0.0, []
    return sum(vals)/len(vals), max(vals), visible_views


def to_occlusion_level(vis_avg: float) -> int:
    """
    Map visibility average to occlusion level based on realistic thresholds.

    UPDATED: Adjusted thresholds based on real-world data analysis from Phase 2.
    Previous thresholds (0.99, 0.75, 0.5) were too idealistic for dense urban scenes.
    New thresholds reflect actual visibility distribution in autonomous driving scenarios.

    Based on Phase 2 analysis:
    - Max observed visibility: ~0.16 (16%)
    - Mean visibility: ~0.021 (2.1%)
    - Distribution: 0.0 to 0.16

    Realistic thresholds for autonomous driving:
    - Level 1 (Visible): >= 10% visibility (good for training)
    - Level 2 (Partially Occluded): >= 5% visibility (usable for training)
    - Level 3 (Largely Occluded): >= 1% visibility (marginal for training)
    - Level 4 (Highly Occluded): < 1% visibility (not suitable for training)
    """
    if vis_avg >= 0.10:  # 10% - Visible (good for training)
        return 1
    if vis_avg >= 0.05:  # 5% - Partially occluded (usable for training)
        return 2
    if vis_avg >= 0.01:  # 1% - Largely occluded (marginal for training)
        return 3
    return 4  # < 1% - Highly occluded (not suitable for training)


def calculate_bev_weight(per_cam: Dict[str, float]) -> float:
    """
    Calculate weight for BEV transformation based on camera coverage.

    ENHANCEMENT: For BEVFusion compatibility - provides weight for perspective-to-BEV transformation
    based on multi-view consistency and coverage.
    """
    if not per_cam:
        return 0.0

    # Define camera groups for different viewing angles
    front_cams = ['60_front', '120_front']
    side_cams = ['120_left', '120_right']
    back_cams = ['120_back', 'left_back', 'right_back']

    # Calculate visibility for each group
    front_vis = np.mean([per_cam.get(cam, 0) for cam in front_cams if per_cam.get(cam) is not None])
    side_vis = np.mean([per_cam.get(cam, 0) for cam in side_cams if per_cam.get(cam) is not None])
    back_vis = np.mean([per_cam.get(cam, 0) for cam in back_cams if per_cam.get(cam) is not None])

    # Weight based on multi-view consistency (higher weight for objects visible from multiple angles)
    view_groups = [front_vis, side_vis, back_vis]
    valid_views = [v for v in view_groups if v > 0]

    if not valid_views:
        return 0.0

    # Multi-view consistency bonus
    consistency_bonus = len(valid_views) / 3.0  # 0.33, 0.67, or 1.0
    avg_visibility = np.mean(valid_views)

    return min(avg_visibility * (1.0 + consistency_bonus * 0.5), 1.0)


def calculate_reliability_weight(stats_total: Dict) -> float:
    """
    Calculate reliability weight based on sampling statistics.

    ENHANCEMENT: For training sample weighting - higher weight for more reliable visibility estimates.
    """
    samples = stats_total.get('samples', 0)
    hits = stats_total.get('hits', 0)

    if samples == 0:
        return 0.0

    # Sample density factor (more samples = more reliable)
    sample_factor = min(samples / 1000.0, 1.0)  # Normalize to 1000 samples

    # Hit rate factor (higher hit rate = more reliable depth data)
    hit_rate = hits / samples if samples > 0 else 0.0
    hit_factor = hit_rate

    # Combined reliability
    reliability = (sample_factor * 0.6 + hit_factor * 0.4)

    return min(reliability, 1.0)


def calculate_occupancy_confidence(vis_avg: float, reliability: float = 1.0) -> float:
    """
    Calculate occupancy confidence for FlashOCC compatibility.

    ENHANCEMENT: For occupancy networks - confidence in voxel occupancy predictions.
    """
    if vis_avg <= 0:
        return 0.0

    # Base confidence from visibility
    base_confidence = vis_avg

    # Adjust by reliability
    adjusted_confidence = base_confidence * reliability

    # Apply sigmoid-like curve to enhance discrimination
    # High visibility -> high confidence, low visibility -> low confidence
    confidence = 1.0 / (1.0 + np.exp(-5.0 * (adjusted_confidence - 0.5)))

    return min(confidence, 1.0)


def estimate_spatial_uncertainty(stats_total: Dict) -> float:
    """
    Estimate spatial uncertainty for the visibility calculation.

    ENHANCEMENT: Provides uncertainty estimate for downstream model training.
    """
    samples = stats_total.get('samples', 0)
    hits = stats_total.get('hits', 0)

    if samples == 0:
        return 1.0  # Maximum uncertainty

    # Uncertainty decreases with more samples and higher hit rate
    sample_uncertainty = 1.0 / (1.0 + samples / 100.0)  # Decreases with more samples
    hit_rate = hits / samples if samples > 0 else 0.0
    hit_uncertainty = 1.0 - hit_rate  # Lower uncertainty with higher hit rate

    # Combined uncertainty
    uncertainty = (sample_uncertainty * 0.4 + hit_uncertainty * 0.6)

    return min(max(uncertainty, 0.0), 1.0)


def generate_voxel_visibility_placeholder(box3d: Dict, per_cam: Dict[str, float], voxel_size: float = 0.2) -> Dict:
    """
    Generate placeholder voxel-level visibility for FlashOCC compatibility.

    ENHANCEMENT: For occupancy networks - provides per-voxel visibility estimates.
    Note: This is a simplified implementation. Full implementation would require
    3D voxel grid discretization and per-voxel visibility calculation.
    """
    # For now, return a simplified representation
    # In full implementation, this would discretize the 3D bounding box into voxels
    # and calculate visibility for each voxel

    surface_pts = box3d.get('surface_pts', np.array([]))
    if len(surface_pts) == 0:
        return {
            "voxel_size": voxel_size,
            "voxel_count": 0,
            "avg_voxel_visibility": 0.0,
            "implementation": "placeholder"
        }

    # Calculate bounding box dimensions
    min_coords = np.min(surface_pts, axis=0)
    max_coords = np.max(surface_pts, axis=0)
    dimensions = max_coords - min_coords

    # Estimate voxel count
    voxel_count = int(np.prod(dimensions / voxel_size))

    # Use average camera visibility as proxy for voxel visibility
    valid_vis = [v for v in per_cam.values() if v is not None]
    avg_visibility = np.mean(valid_vis) if valid_vis else 0.0

    return {
        "voxel_size": voxel_size,
        "voxel_count": voxel_count,
        "avg_voxel_visibility": float(avg_visibility),
        "bbox_dimensions": dimensions.tolist(),
        "implementation": "placeholder"
    }


def check_geometric_consistency(per_cam: Dict[str, float]) -> float:
    """
    Check geometric consistency across camera views.

    ENHANCEMENT: Validates that visibility scores are geometrically consistent
    across different camera viewpoints.
    """
    valid_scores = [v for v in per_cam.values() if v is not None]

    if len(valid_scores) < 2:
        return 1.0 if len(valid_scores) == 1 else 0.0

    # Calculate variance in visibility scores
    mean_vis = np.mean(valid_scores)
    variance = np.var(valid_scores)

    # Consistency score: lower variance = higher consistency
    # Use exponential decay to penalize high variance
    consistency = np.exp(-variance * 5.0)  # Scale factor to adjust sensitivity

    return min(max(consistency, 0.0), 1.0)


def calculate_surface_area(box3d: Dict) -> float:
    """
    Calculate approximate surface area of the 3D bounding box.

    Used for sample density calculations.
    """
    surface_pts = box3d.get('surface_pts', np.array([]))
    if len(surface_pts) == 0:
        return 1.0  # Default to avoid division by zero

    # Estimate surface area from bounding box of surface points
    min_coords = np.min(surface_pts, axis=0)
    max_coords = np.max(surface_pts, axis=0)
    dimensions = max_coords - min_coords

    # Surface area of rectangular box: 2(lw + lh + wh)
    l, w, h = dimensions
    surface_area = 2 * (l*w + l*h + w*h)

    return max(surface_area, 1.0)  # Ensure positive area
