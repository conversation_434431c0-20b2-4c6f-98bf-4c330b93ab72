## Brief overview
This rule file establishes guidelines for developing the vision-aware annotations system, which processes multi-camera vehicle data for visibility analysis and annotation generation. The system handles 3D point cloud data, multi-angle camera feeds, and generates visibility masks for autonomous vehicle training.

## Communication style
- Use precise technical terminology when discussing computer vision, 3D geometry, and sensor fusion concepts
- Provide clear explanations of coordinate transformations and projection mathematics
- Include visual descriptions of camera angles (60° front, 120° front/back/left/right, left/right back)
- Reference specific file formats: .pcd for point clouds, .jpg for camera images, .json for annotations

## Development workflow
- Always validate data pipeline integrity before implementing new features
- Test visibility calculations with known geometric configurations first
- Use batch processing scripts for large datasets (clip_dataset_1 structure)
- Maintain backward compatibility with existing output formats in outputs/ directories
- Document any changes to sphere projection algorithms in visibility modules

## Coding best practices
- Use descriptive variable names for camera parameters (e.g., `camera_60_front`, `camera_120_left`)
- Implement proper error handling for missing sensor data or calibration files
- Structure code to handle both single-frame and batch processing modes
- Validate input data formats before processing (check calibrated_sensor.pb.txt existence)
- Use consistent coordinate system conventions across all modules

## Project context
- System processes Waymo Open Dataset format with custom extensions
- Handles 7 camera angles: 60° front, 120° front/back/left/right, left back, right back
- Generates visibility annotations for 3D object detection training
- Supports both real-time processing and batch dataset generation
- Maintains compatibility with existing training pipelines

## Data handling guidelines
- Always check for calibrated_sensor.pb.txt before processing camera data
- Validate timestamp synchronization between cameras and LiDAR
- Handle missing camera feeds gracefully with appropriate warnings
- Ensure consistent coordinate frame transformations (vehicle to camera to image)
- Maintain original directory structure for output compatibility

## Testing strategies
- Use visibility_demo_data for initial validation of new features
- Compare outputs against known good results in validation_test directories
- Test sphere projection accuracy with synthetic geometric configurations
- Validate annotation completeness for all 7 camera angles
- Run integration tests across full pipeline (run_batch.py)

## Performance considerations
- Optimize batch processing for large datasets (clip_dataset_1 scale)
- Minimize memory usage when processing high-resolution multi-camera data
- Use efficient data structures for 3D point cloud operations
- Implement parallel processing where possible for independent camera views
- Cache calibration data to avoid repeated file I/O