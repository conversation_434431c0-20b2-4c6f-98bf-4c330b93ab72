from typing import Dict, <PERSON><PERSON>, List
import numpy as np
import logging

logger = logging.getLogger(__name__)

def fuse_camera_visibility(per_cam: Dict[str, float], include_noFOV_as_zero=False) -> Tuple[float, float, List[str]]:
    vals = []
    visible_views = []
    for cam, r in per_cam.items():
        if r is None:
            if include_noFOV_as_zero:
                vals.append(0.0)
            continue
        vals.append(r)
        if r >= 0.15:
            visible_views.append(cam)
    if not vals:
        return 0.0, 0.0, []
    return sum(vals)/len(vals), max(vals), visible_views


def to_occlusion_level(vis_avg: float) -> int:
    if vis_avg >= 0.99:
        return 1
    if vis_avg >= 0.75:
        return 2
    if vis_avg >= 0.5:
        return 3
    return 4
