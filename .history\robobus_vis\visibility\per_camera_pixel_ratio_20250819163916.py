import numpy as np
import logging
import cv2
import os

# Set up logging for debugging visibility calculations
logger = logging.getLogger(__name__)


def visibility_via_zbuffer(box3d, cam, depth_img: np.ndarray, tau_base=0.3, tau_scale=0.02):
    """Compute visible pixel ratio inside projected box mask by comparing depth.

    CORRECTED: Fixed depth comparison logic to properly identify visible surfaces.
    Object is visible if scene depth is close to object depth within tolerance.

    Returns: (ratio or 0.0, stats dict) where stats includes samples, hits, avg_z, hit_rate.
    """
    H, W = depth_img.shape
    pts = box3d['surface_pts']  # precomputed Nx3
    if pts.size == 0:
        return 0.0, {"samples": 0, "hits": 0, "hit_rate": 0.0}
    uvz = cam.project(pts)
    u = np.round(uvz[:,0]).astype(int)
    v = np.round(uvz[:,1]).astype(int)
    z = uvz[:,2]
    in_img = np.isfinite(z) & (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[in_img], v[in_img], z[in_img]
    if len(u) == 0:
        return 0.0, {"samples": 0, "hits": 0, "hit_rate": 0.0}
    D = depth_img[v, u]
    tau = np.maximum(tau_base, tau_scale * z)
    finite = np.isfinite(D)

    # CRITICAL FIX: Corrected depth comparison logic
    # Object is visible if scene depth is close to object depth (within tolerance)
    # Previous: (z - D) <= tau  [INCORRECT - would mark occluded objects as visible]
    # Corrected: abs(D - z) <= tau  [CORRECT - checks if depths are approximately equal]
    visible = finite & (np.abs(D - z) <= tau)

    samples = int(len(visible))
    hits = int(finite.sum())
    hit_rate = float(hits) / float(max(1, samples))
    ratio = float(visible.sum()) / float(samples)

    # Add debug logging for visibility calculation
    logger.debug(f"Visibility calculation: samples={samples}, hits={hits}, visible={visible.sum()}, ratio={ratio:.3f}")

    stats = {"samples": samples, "hits": hits, "hit_rate": hit_rate, "avg_z": float(np.mean(z))}
    return ratio, stats


def visibility_via_zbuffer_diagnostic(box3d, cam, depth_img: np.ndarray, tau_base=0.3, tau_scale=0.02,
                                     debug_dir=None, obj_id=None, cam_name=None):
    """
    Enhanced visibility calculation with detailed diagnostics and visualization.

    DIAGNOSTIC VERSION: Provides detailed logging and optional visualization for debugging
    the root cause of low visibility scores.

    Args:
        box3d: Dictionary containing 'surface_pts' (Nx3 array)
        cam: Camera object with projection capabilities
        depth_img: Scene depth image (H, W)
        tau_base: Base tolerance for depth comparison
        tau_scale: Scale factor for distance-dependent tolerance
        debug_dir: Optional directory to save diagnostic images
        obj_id: Object ID for debugging
        cam_name: Camera name for debugging

    Returns:
        (ratio, stats, diagnostic_info)
    """
    H, W = depth_img.shape
    pts = box3d['surface_pts']

    if pts.size == 0:
        return 0.0, {"samples": 0, "hits": 0, "hit_rate": 0.0}, {}

    # Project points to image
    uvz = cam.project(pts)
    u = np.round(uvz[:,0]).astype(int)
    v = np.round(uvz[:,1]).astype(int)
    z = uvz[:,2]

    # Filter points within image bounds
    in_img = np.isfinite(z) & (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[in_img], v[in_img], z[in_img]

    if len(u) == 0:
        return 0.0, {"samples": 0, "hits": 0, "hit_rate": 0.0}, {}

    # Get scene depths at projected locations
    D = depth_img[v, u]
    tau = np.maximum(tau_base, tau_scale * z)
    finite = np.isfinite(D)

    # Calculate depth differences and visibility
    depth_diff = np.abs(D - z)
    visible = finite & (depth_diff <= tau)

    # Detailed statistics
    samples = int(len(visible))
    hits = int(finite.sum())
    visible_count = int(visible.sum())
    hit_rate = float(hits) / float(max(1, samples))
    ratio = float(visible_count) / float(samples)

    # DIAGNOSTIC LOGGING - Critical for understanding the problem
    logger.info(f"=== DIAGNOSTIC ANALYSIS ===")
    logger.info(f"Object {obj_id}, Camera {cam_name}")
    logger.info(f"Total surface points: {len(pts)}")
    logger.info(f"Points in image bounds: {samples}")
    logger.info(f"Points with finite scene depth: {hits}")
    logger.info(f"Points marked as visible: {visible_count}")
    logger.info(f"Visibility ratio: {ratio:.6f}")

    # Analyze tau values
    logger.info(f"Tau values - min: {tau.min():.4f}, max: {tau.max():.4f}, mean: {tau.mean():.4f}")

    # Analyze depth differences
    valid_depth_diff = depth_diff[finite]
    if len(valid_depth_diff) > 0:
        logger.info(f"Depth differences - min: {valid_depth_diff.min():.4f}, max: {valid_depth_diff.max():.4f}, mean: {valid_depth_diff.mean():.4f}")
        logger.info(f"Percentage of points with depth_diff <= tau: {(valid_depth_diff <= tau[finite]).mean()*100:.2f}%")

    # Sample detailed analysis for first few points
    n_sample = min(10, len(z))
    logger.info(f"Sample analysis (first {n_sample} points):")
    for i in range(n_sample):
        if finite[i]:
            logger.info(f"  Point {i}: z={z[i]:.3f}, D={D[i]:.3f}, |D-z|={depth_diff[i]:.3f}, tau={tau[i]:.3f}, visible={visible[i]}")
        else:
            logger.info(f"  Point {i}: z={z[i]:.3f}, D=inf, visible=False")

    # Create diagnostic info
    diagnostic_info = {
        "total_surface_points": len(pts),
        "points_in_bounds": samples,
        "points_with_scene_depth": hits,
        "visible_points": visible_count,
        "tau_stats": {"min": float(tau.min()), "max": float(tau.max()), "mean": float(tau.mean())},
        "depth_diff_stats": {
            "min": float(valid_depth_diff.min()) if len(valid_depth_diff) > 0 else 0,
            "max": float(valid_depth_diff.max()) if len(valid_depth_diff) > 0 else 0,
            "mean": float(valid_depth_diff.mean()) if len(valid_depth_diff) > 0 else 0
        }
    }

    # Optional visualization
    if debug_dir and obj_id is not None and cam_name:
        create_diagnostic_visualization(depth_img, u, v, z, D, visible, finite,
                                      debug_dir, obj_id, cam_name)

    stats = {"samples": samples, "hits": hits, "hit_rate": hit_rate, "avg_z": float(np.mean(z))}
    return ratio, stats, diagnostic_info


def create_diagnostic_visualization(depth_img, u, v, z, D, visible, finite, debug_dir, obj_id, cam_name):
    """
    Create diagnostic visualization showing why points are classified as visible/occluded.

    This is CRITICAL for understanding the root cause of low visibility scores.
    """
    try:
        os.makedirs(debug_dir, exist_ok=True)

        H, W = depth_img.shape

        # Create visualization image (3-channel for color coding)
        vis_img = np.zeros((H, W, 3), dtype=np.uint8)

        # Normalize depth image for visualization (0-255)
        depth_normalized = depth_img.copy()
        finite_depths = depth_normalized[np.isfinite(depth_normalized)]
        if len(finite_depths) > 0:
            min_depth, max_depth = finite_depths.min(), finite_depths.max()
            depth_normalized = np.clip((depth_normalized - min_depth) / (max_depth - min_depth) * 255, 0, 255)
            depth_normalized[~np.isfinite(depth_img)] = 0
        else:
            depth_normalized = np.zeros_like(depth_normalized)

        # Set background to depth image (grayscale)
        vis_img[:, :, 0] = depth_normalized
        vis_img[:, :, 1] = depth_normalized
        vis_img[:, :, 2] = depth_normalized

        # Color-code projected points
        for i in range(len(u)):
            ui, vi = u[i], v[i]
            if 0 <= vi < H and 0 <= ui < W:
                if not finite[i]:
                    # Blue for points with no scene depth (infinite depth)
                    vis_img[vi, ui] = [255, 0, 0]  # Blue
                elif visible[i]:
                    # Green for visible points
                    vis_img[vi, ui] = [0, 255, 0]  # Green
                else:
                    # Red for occluded points
                    vis_img[vi, ui] = [0, 0, 255]  # Red

        # Save the diagnostic image
        filename = f"diagnostic_obj{obj_id}_{cam_name}.png"
        filepath = os.path.join(debug_dir, filename)
        cv2.imwrite(filepath, vis_img)

        # Also create a detailed analysis image with text overlay
        analysis_img = vis_img.copy()

        # Add text information
        visible_count = visible.sum()
        total_count = len(visible)
        finite_count = finite.sum()

        text_lines = [
            f"Object {obj_id} - {cam_name}",
            f"Total points: {total_count}",
            f"Finite depth: {finite_count}",
            f"Visible: {visible_count}",
            f"Ratio: {visible_count/total_count:.4f}",
            "",
            "Legend:",
            "Green = Visible",
            "Red = Occluded",
            "Blue = No scene depth",
            "Gray = Scene depth"
        ]

        y_offset = 30
        for line in text_lines:
            cv2.putText(analysis_img, line, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            y_offset += 25

        analysis_filename = f"analysis_obj{obj_id}_{cam_name}.png"
        analysis_filepath = os.path.join(debug_dir, analysis_filename)
        cv2.imwrite(analysis_filepath, analysis_img)

        logger.info(f"Diagnostic images saved: {filepath}, {analysis_filepath}")

    except Exception as e:
        logger.error(f"Failed to create diagnostic visualization: {e}")
