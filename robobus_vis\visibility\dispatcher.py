from typing import Any, Dict, <PERSON><PERSON>, Optional
import numpy as np
import logging

from .per_camera_pixel_ratio import visibility_via_zbuffer, visibility_via_zbuffer_diagnostic
from .surface_ray_sampling import visibility_via_ray_sampling
from .sphere_projection import visibility_via_sphere_projection

logger = logging.getLogger(__name__)


def compute_visibility(
    box3d: Dict,
    cam,
    depth_img: np.ndarray,
    cfg: Dict,
    method_arg: str,
    samples: int,
    enable_diagnostics: bool,
    debug_dir: Optional[str],
    obj_id: Any,
    cam_name: str,
) -> Tuple[float, Dict]:
    """
    Route to the requested visibility algorithm with robust fallback.

    method_arg: one of {'zbuffer','rays','sphere','auto'}
    If 'auto', uses cfg['visibility']['method_primary'] and falls back to cfg['visibility']['method_fallback']
    """
    vis_cfg = cfg.get('visibility', {})

    def run_one(method: str) -> <PERSON><PERSON>[float, Dict]:
        if method == 'zbuffer':
            if enable_diagnostics:
                r, st, _diag = visibility_via_zbuffer_diagnostic(
                    box3d,
                    cam,
                    depth_img,
                    tau_base=vis_cfg.get('tau_base_m', 0.3),
                    tau_scale=vis_cfg.get('tau_scale_per_m', 0.02),
                    debug_dir=debug_dir,
                    obj_id=obj_id,
                    cam_name=cam_name,
                )
            else:
                r, st = visibility_via_zbuffer(
                    box3d,
                    cam,
                    depth_img,
                    tau_base=vis_cfg.get('tau_base_m', 0.3),
                    tau_scale=vis_cfg.get('tau_scale_per_m', 0.02),
                )
            return r, st
        elif method == 'rays':
            return visibility_via_ray_sampling(box3d, cam, depth_img, samples=samples)
        elif method == 'sphere':
            return visibility_via_sphere_projection(
                box3d,
                cam,
                depth_img,
                sphere_radius_m=vis_cfg.get('sphere_radius_m', 0.15),
                tau_base_m=vis_cfg.get('tau_base_m', 0.3),
                tau_scale_per_m=vis_cfg.get('tau_scale_per_m', 0.02),
                occlusion_fraction_thr=vis_cfg.get('occlusion_fraction_thr', 0.2),
                min_neighbors=vis_cfg.get('min_neighbors', 3),
                treat_no_depth_as_visible=vis_cfg.get('treat_no_depth_as_visible', True),
                max_window_px=vis_cfg.get('max_window_px', 15),
            )
        else:
            raise ValueError(f"Unknown visibility method: {method}")

    def insufficient(st: Dict, method: str) -> bool:
        min_samples = vis_cfg.get('min_pixels_for_zbuffer', 100)
        if method == 'zbuffer':
            return st.get('samples', 0) < min_samples
        if method == 'sphere':
            return st.get('considered', 0) < min_samples
        return False

    if method_arg != 'auto':
        r, st = run_one(method_arg)
        return r, st

    primary = vis_cfg.get('method_primary', 'zbuffer')
    fallback = vis_cfg.get('method_fallback', 'rays')

    try:
        r, st = run_one(primary)
    except Exception as e:
        logger.warning(f"Primary visibility method '{primary}' failed: {e}")
        r, st = 0.0, {}

    if insufficient(st, primary):
        try:
            r2, st2 = run_one(fallback)
            return r2, st2
        except Exception as e:
            logger.warning(f"Fallback visibility method '{fallback}' failed: {e}")
            return r, st

    return r, st

