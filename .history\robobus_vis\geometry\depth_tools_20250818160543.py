import numpy as np
import logging

logger = logging.getLogger(__name__)


def pointcloud_to_depth(pts_base: np.ndarray, cam, H: int, W: int, fill_min_neighbors: int = 0):
    """Project points to depth image using z-buffer (min depth per pixel).
    Returns depth image (H,W) with np.inf as missing, hit_count, and (u,v,z) arrays after masking.
    """
    uvz = cam.project(pts_base)
    u = np.round(uvz[:,0]).astype(int)
    v = np.round(uvz[:,1]).astype(int)
    z = uvz[:,2]
    mask = np.isfinite(z) & (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[mask], v[mask], z[mask]
    D = np.full((H, W), np.inf, dtype=np.float32)
    for ui, vi, zi in zip(u, v, z):
        if zi < D[vi, ui]:
            D[vi, ui] = zi
    hit = int(np.isfinite(D).sum())
    return D, hit, (u, v, z)
