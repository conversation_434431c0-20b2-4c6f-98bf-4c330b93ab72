import numpy as np


def box_corners(center, l, w, h, yaw):
    """Return 8 corners of an oriented 3D box in base frame.
    l: length (x), w: width (y), h: height (z), yaw around +z.
    center: (x,y,z)
    """
    cx, cy, cz = center
    # local corners around origin
    x = l / 2.0
    y = w / 2.0
    z = h / 2.0
    corners = np.array([
        [ x,  y,  z], [ x, -y,  z], [-x, -y,  z], [-x,  y,  z],
        [ x,  y, -z], [ x, -y, -z], [-x, -y, -z], [-x,  y, -z]
    ], dtype=float)
    c, s = np.cos(yaw), np.sin(yaw)
    Rz = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=float)
    corners = (Rz @ corners.T).T
    corners += np.array([cx, cy, cz])
    return corners


def sample_box_surface(center, l, w, h, yaw, samples_per_face=1000):
    """Uniform-ish sampling on the 6 faces of the box.
    Returns Nx3 points in base frame.
    """
    cx, cy, cz = center
    # define 6 faces in local frame (z-up)
    faces = []
    # +x, -x faces
    faces.append((np.array([1,0,0]), w, h))
    faces.append((np.array([-1,0,0]), w, h))
    # +y, -y faces
    faces.append((np.array([0,1,0]), l, h))
    faces.append((np.array([0,-1,0]), l, h))
    # +z, -z faces
    faces.append((np.array([0,0,1]), l, w))
    faces.append((np.array([0,0,-1]), l, w))

    pts = []
    c, s = np.cos(yaw), np.sin(yaw)
    Rz = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=float)

    for normal, a, b in faces:
        # Construct local axes on the face plane
        if normal[0] != 0:
            u_dir = np.array([0,1,0])
            v_dir = np.array([0,0,1])
            offset = np.array([np.sign(normal[0]) * (l/2), 0, 0])
        elif normal[1] != 0:
            u_dir = np.array([1,0,0])
            v_dir = np.array([0,0,1])
            offset = np.array([0, np.sign(normal[1]) * (w/2), 0])
        else:
            u_dir = np.array([1,0,0])
            v_dir = np.array([0,1,0])
            offset = np.array([0, 0, np.sign(normal[2]) * (h/2)])
        # Stratified grid sampling
        n = int(np.sqrt(samples_per_face))
        if n < 1:
            n = 1
        for i in range(n):
            for j in range(n):
                u = (i + 0.5) / n - 0.5
                v = (j + 0.5) / n - 0.5
                p_local = offset + u * a * u_dir + v * b * v_dir
                p_world = (Rz @ p_local) + np.array([cx, cy, cz])
                pts.append(p_world)
    return np.array(pts, dtype=float)
