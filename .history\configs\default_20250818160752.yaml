sync:
  enable: true
  max_sync_dt_ms: 30
  pose_required: true

visibility:
  method_primary: zbuffer
  method_fallback: rays
  tau_base_m: 0.2  # REDUCED: More precise depth tolerance
  tau_scale_per_m: 0.015  # REDUCED: More precise depth tolerance
  min_pixels_for_zbuffer: 100  # REDUCED: Lower threshold to process more objects
  min_depth_hits_for_zbuffer: 30  # REDUCED: Lower threshold to process more objects
  per_view_visible_thresh: 0.10  # REDUCED: Lower threshold for visibility
  include_noFOV_as_zero: false
  adaptive_tolerance: true  # NEW: Enable adaptive tolerance based on distance
  quality_threshold: 0.3  # NEW: Minimum quality score for results

fov_filter:
  enabled: true
  use_corner_check: false
  vfov_deg_default: 50.0  # INCREASED: More permissive vertical FOV
  debug_filtering: true  # NEW: Enable debug logging for filtering
  fallback_on_filter: true  # NEW: Allow fallback when filtering is too aggressive

# RELAXED CAMERA FOVs: Increased to prevent over-filtering
camera_fovs_deg:
  60_front:   {h: 70.0,  v: 50.0}   # INCREASED: from 60°/40° to 70°/50°
  120_front:  {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  120_back:   {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  120_left:   {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  120_right:  {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  left_back:  {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  right_back: {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°

# RELAXED EGO SECTORS: Increased to prevent over-filtering
ego_sector_map_deg:
  60_front:   {center:   0.0, half: 45.0}   # INCREASED: from 30° to 45°
  120_front:  {center:   0.0, half: 75.0}   # INCREASED: from 60° to 75°
  120_left:   {center:  90.0, half: 75.0}   # INCREASED: from 60° to 75°
  120_right:  {center: -90.0, half: 75.0}   # INCREASED: from 60° to 75°
  120_back:   {center: 180.0, half: 75.0}   # INCREASED: from 60° to 75°
  left_back:  {center: 150.0, half: 75.0}   # INCREASED: from 60° to 75°
  right_back: {center: -150.0, half: 75.0}  # INCREASED: from 60° to 75°

training_filter:
  drop_if_level_ge: 4
  min_vis_avg: 0.3  # REDUCED: Lower threshold to include more training samples
  loss_weight_by_confidence: true
  use_continuous_weights: true  # NEW: Use continuous weights instead of discrete levels
  bev_fusion_compatible: true  # NEW: Enable BEVFusion compatibility features
  flashocc_compatible: true  # NEW: Enable FlashOCC compatibility features

camera_map:
  60_front: H60L-E08160504
  120_front: H120L-E06170513
  120_back: H120L-E06170599
  120_left: H120L-F05160609
  120_right: H120L-E12190550
  left_back: H120L-P05160612
  right_back: H120L-F05160614
