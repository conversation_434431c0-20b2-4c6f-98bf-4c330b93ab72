import numpy as np
import logging
from typing import Dict, Tuple

logger = logging.getLogger(__name__)


def _pixel_radius_from_metric(cam, depth_z: float, sphere_radius_m: float, max_window_px: int) -> int:
    """Approximate projected pixel radius for a small sphere of metric radius at depth z.
    Uses camera intrinsics to map meters→pixels; clamps to [1, max_window_px].
    """
    if not np.isfinite(depth_z) or depth_z <= 0:
        return 1
    fx = cam.K[0, 0]
    fy = cam.K[1, 1]
    # project metric radius to pixels along u and v axes
    r_u = fx * sphere_radius_m / depth_z
    r_v = fy * sphere_radius_m / depth_z
    r = int(max(1, min(max_window_px, np.ceil(max(r_u, r_v)))))
    return r


def visibility_via_sphere_projection(
    box3d: Dict,
    cam,
    depth_img: np.ndarray,
    *,
    sphere_radius_m: float = 0.15,
    tau_base_m: float = 0.3,
    tau_scale_per_m: float = 0.02,
    occlusion_fraction_thr: float = 0.2,
    min_neighbors: int = 3,
    treat_no_depth_as_visible: bool = True,
    max_window_px: int = 15,
) -> Tuple[float, Dict]:
    """
    Sphere projection visibility estimation.

    For each sampled surface point on the 3D box, we:
      1) Project to image to get (u,v,z)
      2) Compute a pixel window radius corresponding to a metric sphere radius at depth z
      3) Within the window, count neighbors whose scene depth is closer than (z - tau)
      4) Mark the point visible if the fraction of closer neighbors < occlusion_fraction_thr
         - If there are no finite depths in the window, classify based on treat_no_depth_as_visible

    Args:
      box3d: dict with key 'surface_pts' (N x 3) points in base/world frame
      cam: Camera object with .K and .project()
      depth_img: (H, W) depth map (np.inf for missing)

    Returns:
      (visibility_ratio, stats)
    """
    H, W = depth_img.shape

    pts = box3d.get('surface_pts', None)
    if pts is None or len(pts) == 0:
        return 0.0, {"samples": 0, "considered": 0, "visible": 0}

    uvz = cam.project(pts)
    u = np.round(uvz[:, 0]).astype(int)
    v = np.round(uvz[:, 1]).astype(int)
    z = uvz[:, 2]

    in_img = np.isfinite(z) & (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u = u[in_img]
    v = v[in_img]
    z = z[in_img]

    if len(u) == 0:
        return 0.0, {"samples": 0, "considered": 0, "visible": 0}

    total = len(u)
    considered = 0
    visible_pts = 0
    sum_r = 0.0

    for i in range(total):
        ui = u[i]
        vi = v[i]
        zi = z[i]

        # Adaptive tolerance (meters)
        tau = max(tau_base_m, tau_scale_per_m * float(zi))

        r = _pixel_radius_from_metric(cam, float(zi), sphere_radius_m, max_window_px)
        sum_r += r

        u0 = int(max(0, ui - r))
        u1 = int(min(W, ui + r + 1))
        v0 = int(max(0, vi - r))
        v1 = int(min(H, vi + r + 1))

        win = depth_img[v0:v1, u0:u1]
        if win.size == 0:
            # No pixels to evaluate; skip counting as considered
            continue

        finite = np.isfinite(win)
        n_finite = int(finite.sum())

        if n_finite == 0:
            considered += 1
            if treat_no_depth_as_visible:
                visible_pts += 1
            continue

        # Count neighbors that are in front of the sphere surface (occluders)
        closer = (win[finite] < (zi - tau))
        frac_closer = float(closer.sum()) / float(n_finite)

        considered += 1
        if frac_closer < occlusion_fraction_thr:
            visible_pts += 1

    ratio = float(visible_pts) / float(max(1, considered))
    stats = {
        "samples": int(total),
        "considered": int(considered),
        "visible": int(visible_pts),
        "avg_pixel_radius": float(sum_r / max(1, considered)),
        "avg_z": float(np.mean(z)) if len(z) > 0 else 0.0,
    }

    logger.debug(
        f"SphereProjection: samples={total}, considered={considered}, visible={visible_pts}, "
        f"ratio={ratio:.3f}, avg_r={stats['avg_pixel_radius']:.2f}"
    )

    return ratio, stats

