import argparse
from pathlib import Path
import json
import yaml
import numpy as np
import cv2
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)
from ..io.dataset_loader import ClipDataset
from ..io.calib_parser import parse_calibrated_sensor_pb_txt
# Removed PoseStream import - no longer needed for simplified pipeline
from ..calib.camera_model import Camera
from ..calib.transforms import transform_points, inv_T
from ..geometry.depth_tools import pointcloud_to_depth, apply_depth_dilation
from ..geometry.box3d import sample_box_surface, sample_box_surface_adaptive, box_corners
from ..visibility.dispatcher import compute_visibility
# Simplified imports - only keep essential visibility functions
from ..visibility.fusion import (
    fuse_camera_visibility, to_occlusion_level,
    validate_occlusion_consistency, calculate_visibility_statistics
)
# Removed visibility_confidence import - not needed in simplified schema
# Removed sync_compensation import - data is pre-synchronized
from ..visibility.fov_filter import ego_sector_gate, cam_fov_gate_point, cam_fov_gate_corners
from ..vis.draw2d import draw_box_projections, overlay_visibility_score, make_mosaic
from ..vis.exporters import save_image

# End-to-end batch with projection visualization and sync fields


def load_pcd_xyz(pcd_path: Path) -> np.ndarray:
    """Load XYZ coordinates from PCD file, handling both plain text and PCD format."""
    try:
        # First try loading as plain text (for backward compatibility)
        pts = np.loadtxt(str(pcd_path), usecols=(0,1,2))
        if pts.ndim == 1:
            pts = pts.reshape(1, -1)
        return pts.astype(float)
    except Exception as e:
        # Try parsing as PCD format
        try:
            return load_pcd_format(pcd_path)
        except Exception as e2:
            logger.error(f"Failed to load point cloud from {pcd_path}: {e2}")
            return np.empty((0,3), dtype=float)


def load_pcd_format(pcd_path: Path) -> np.ndarray:
    """Load XYZ coordinates from PCD format file."""
    points = []
    data_started = False

    with open(pcd_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('DATA'):
                data_started = True
                continue

            if data_started and line:
                try:
                    # Split line and take first 3 values as x, y, z
                    parts = line.split()
                    if len(parts) >= 3:
                        x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                        points.append([x, y, z])
                except ValueError:
                    continue  # Skip invalid lines

    if not points:
        logger.warning(f"No valid points found in PCD file: {pcd_path}")
        return np.empty((0,3), dtype=float)

    return np.array(points, dtype=float)


def process_single_object(obj, cams, depth_imgs, cfg, args, fov_cfg, sectors, cam_fov_rad, use_corner, drawn_images=None):
    """
    Process visibility calculation for a single object.

    PHASE 4 OPTIMIZATION: Extracted for parallel processing.
    Each object's visibility calculation is independent and can be parallelized.

    Args:
        obj: Object dictionary from JSON
        cams: Dictionary of camera objects
        depth_imgs: Pre-computed depth images for all cameras
        cfg: Configuration dictionary
        args: Command line arguments
        fov_cfg: FOV configuration
        sectors: Ego sector configuration
        cam_fov_rad: Camera FOV in radians
        use_corner: Whether to use corner-based FOV checking
        drawn_images: Optional images for visualization

    Returns:
        (obj_id, obj_aug): Tuple of object ID and augmented visibility data
    """
    try:
        obj_id = obj.get('ObjectID', 'unknown')

        # Extract 3D bounding box
        center = obj['3Dcenter']
        size = obj['3Dsize']
        cx, cy, cz = center['x'], center['y'], center['z']
        l, w, h = size['length'], size['width'], size['height']
        alpha = size.get('alpha', 0.0)

        # Sample surface points
        use_adaptive = cfg.get('surface_sampling', {}).get('adaptive_sampling', False)
        if use_adaptive:
            # For adaptive sampling, we need camera position - use first available camera
            cam_pos = None
            for cam_name, cam in cams.items():
                if cam is not None:
                    cam_pos = cam.T_base_cam[:3, 3]  # Camera position in base frame
                    break

            if cam_pos is not None:
                surf = sample_box_surface_adaptive((cx, cy, cz), l, w, h, alpha, cam_pos,
                                                 target_samples=cfg.get('surface_sampling', {}).get('max_samples_per_object', 4800))
            else:
                # Fallback to uniform sampling
                surf = sample_box_surface((cx, cy, cz), l, w, h, alpha, samples_per_face=800)
        else:
            surf = sample_box_surface((cx, cy, cz), l, w, h, alpha, samples_per_face=800)

        box = {'surface_pts': surf}

        corners = box_corners((cx, cy, cz), l, w, h, alpha)

        # Process each camera
        per_cam = {}
        stats_total = {'samples': 0, 'hits': 0}

        for cam_name, cam in cams.items():
            # FOV gating
            gate_ok = True
            if fov_cfg:
                ok1, r1 = ego_sector_gate((cx,cy,cz), cam_name, sectors)
                if not ok1:
                    gate_ok = False
                else:
                    if use_corner:
                        ok2, r2 = cam_fov_gate_corners(corners, cam.T_base_cam, cam_name, cam_fov_rad)
                    else:
                        ok2, r2 = cam_fov_gate_point((cx,cy,cz), cam.T_base_cam, cam_name, cam_fov_rad)
                    if not ok2:
                        gate_ok = False

            if not gate_ok:
                per_cam[cam_name] = None
                continue

            D, hits, uvz_hits = depth_imgs.get(cam_name, (None, None, None))
            if D is None:
                per_cam[cam_name] = None
                continue

            # Apply depth-hit gating only when primary method is zbuffer
            primary_method = cfg.get('visibility', {}).get('method_primary', 'zbuffer')
            if args.method == 'zbuffer' or (args.method == 'auto' and primary_method == 'zbuffer'):
                min_hits = cfg['visibility'].get('min_depth_hits_for_zbuffer', 50)
                if hits < min_hits:
                    per_cam[cam_name] = None
                    continue

            # Calculate visibility
            enable_diagnostics = cfg['visibility'].get('enable_diagnostics', False)
            debug_dir = cfg['visibility'].get('diagnostic_output_dir', 'outputs/diagnostics') if enable_diagnostics else None

            # Compute visibility via selected method with fallback handling
            r, st = compute_visibility(
                box, cam, D, cfg, args.method, args.samples,
                enable_diagnostics, debug_dir, obj_id, cam_name
            )

            per_cam[cam_name] = r
            stats_total['samples'] += st.get('samples', 0)
            stats_total['hits'] += st.get('hits', 0)

            # Visualization (if enabled and images available)
            if args.viz_cam and drawn_images and cam_name in drawn_images:
                img = drawn_images[cam_name]
                # Note: Skip point cloud visualization in parallel processing for now
                # draw_projected_points(img, cam, pts)  # pts not available in this context
                draw_box_projections(img, cam, corners)
                overlay_visibility_score(img, r)

        # Fuse camera results
        vis_avg, vis_max, visible_views = fuse_camera_visibility(per_cam, include_noFOV_as_zero=cfg['visibility']['include_noFOV_as_zero'])
        level = to_occlusion_level(vis_avg)

        # Apply consistency validation
        is_consistent, corrected_level, reason = validate_occlusion_consistency(vis_avg, visible_views, level)

        if not is_consistent:
            logger.info(f"Object {obj_id}: Occlusion level corrected from {level} to {corrected_level} ({reason})")
            level = corrected_level

        # Calculate additional statistics
        vis_stats = calculate_visibility_statistics(per_cam)

        # Log interesting cases
        if vis_avg > 0.05 or level <= 2:
            logger.info(f"Object {obj_id}: vis_avg={vis_avg:.4f}, level={level}, "
                       f"visible_views={len(visible_views)}, coverage={vis_stats['coverage_ratio']:.2f}")

        # Create output (extended fields)
        best_view = max(((k, v) for k, v in per_cam.items() if v is not None), key=lambda kv: kv[1], default=(None, 0.0))[0]
        obj_aug = {
            "visibility": {
                "per_camera": per_cam,
                "visibility_rate_avg": vis_avg,
                "visibility_rate_max": vis_max,
                "occlusion_level": level,
                "visible_in_views": visible_views,
                "best_view": best_view,
            }
        }

        return obj_id, obj_aug

    except Exception as e:
        logger.error(f"Error processing object {obj.get('ObjectID', 'unknown')}: {e}")
        return obj.get('ObjectID', 'unknown'), None


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--clip_dir', required=True)
    parser.add_argument('--config', default=str(Path(__file__).resolve().parents[2] / 'configs' / 'default.yaml'))
    parser.add_argument('--save_dir', default='./outputs')
    parser.add_argument('--viz_cam', action='store_true')
    parser.add_argument('--method', default='auto', choices=['zbuffer','rays','sphere','auto'])
    parser.add_argument('--samples', type=int, default=2048)
    args = parser.parse_args()

    cfg = yaml.safe_load(open(args.config, 'r'))
    clip = ClipDataset(args.clip_dir)
    ts_list = clip.list_timestamps()

    calibs = parse_calibrated_sensor_pb_txt(str(clip.calib_pb_txt))
    # Removed pose loading - data is pre-synchronized

    cam_map = cfg.get('camera_map', {})
    cams = {}
    for dir_name, sensor_name in cam_map.items():
        if sensor_name not in calibs.cams:
            continue
        c = calibs.cams[sensor_name]
        cams[dir_name] = Camera(
            name=dir_name,
            K=c['K'],
            dist=c['dist'],
            width=c['width'],
            height=c['height'],
            T_base_cam=c['T_base_cam'],
        )

    save_root = Path(args.save_dir) / Path(args.clip_dir).name
    save_root.mkdir(parents=True, exist_ok=True)

    for ts in ts_list:
        paths = clip.frame_paths_by_ts(ts)
        pts = load_pcd_xyz(paths['pcd'])
        j = json.loads(Path(paths['json']).read_text(encoding='utf-8'))
        objects = j.get('result', {}).get('data', [])

        # PHASE 4 OPTIMIZATION: Cache depth maps per camera (expensive operation)
        # Each camera's depth map is the same for all objects in this frame
        depth_imgs = {}
        images_cache = {}

        logger.info(f"Generating depth maps for {len(cams)} cameras...")
        depth_generation_start = time.time()

        for cam_name, cam in cams.items():
            img_path = paths['images'][cam_name]
            if not img_path.exists():
                logger.warning(f"Image not found for camera {cam_name}: {img_path}")
                continue

            H, W = cam.height, cam.width

            # Generate depth map once per camera (most expensive operation)
            cam_start_time = time.time()
            D, hits, uvz_hits = pointcloud_to_depth(pts, cam, H, W)

            # Apply depth dilation to address point cloud density issues
            D_dilated = apply_depth_dilation(D, dilation_size=3)

            depth_imgs[cam_name] = (D_dilated, hits, uvz_hits)

            cam_duration = time.time() - cam_start_time
            logger.debug(f"Camera {cam_name}: depth map generated in {cam_duration:.2f}s ({hits} hits)")

        depth_generation_duration = time.time() - depth_generation_start
        logger.info(f"All depth maps generated in {depth_generation_duration:.2f}s")

        # Load images for visualization if needed
        if args.viz_cam:
            for cam_name, cam in cams.items():
                img_path = paths['images'][cam_name]
                if img_path.exists():
                    img = cv2.imread(str(img_path))
                    if img is not None:
                        images_cache[cam_name] = img

        per_object_aug = {}
        # Prepare per-camera images for drawing once and overlay multiple objects
        drawn_images = {k: v.copy() for k, v in images_cache.items()}

        # PHASE 4 OPTIMIZATION: Parallel processing of objects
        logger.info(f"Processing {len(objects)} objects in parallel...")
        object_processing_start = time.time()

        # Prepare common parameters for all objects
        fov_cfg = cfg.get('fov_filter', {}).get('enabled', True)
        use_corner = cfg.get('fov_filter', {}).get('use_corner_check', False)
        cam_fovs_deg = cfg.get('camera_fovs_deg', {})
        cam_fov_rad = {k: {"h": v.get('h', 120.0)*np.pi/180.0, "v": v.get('v', cfg.get('fov_filter',{}).get('vfov_deg_default',40.0))*np.pi/180.0} for k,v in cam_fovs_deg.items()}
        sector_map = cfg.get('ego_sector_map_deg', {})
        sectors = {k: {"center": v['center']*np.pi/180.0, "half": v['half']*np.pi/180.0} for k,v in sector_map.items()}

        # Determine number of worker threads (use CPU count but limit to reasonable number)
        import os
        max_workers = min(len(objects), os.cpu_count() or 4, 8)  # Limit to 8 threads max
        logger.info(f"Using {max_workers} worker threads for parallel processing")

        # Process objects in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all objects for processing
            future_to_obj = {
                executor.submit(
                    process_single_object,
                    obj, cams, depth_imgs, cfg, args, fov_cfg, sectors, cam_fov_rad, use_corner, drawn_images
                ): obj for obj in objects
            }

            # Collect results as they complete
            completed_objects = 0
            for future in as_completed(future_to_obj):
                obj = future_to_obj[future]
                try:
                    obj_id, obj_aug = future.result()
                    if obj_aug is not None:
                        per_object_aug[obj_id] = obj_aug
                    completed_objects += 1

                    # Log progress every 10 objects
                    if completed_objects % 10 == 0:
                        logger.info(f"Completed {completed_objects}/{len(objects)} objects")

                except Exception as e:
                    logger.error(f"Error processing object {obj.get('ObjectID', 'unknown')}: {e}")

        object_processing_duration = time.time() - object_processing_start
        logger.info(f"All {len(objects)} objects processed in {object_processing_duration:.2f}s "
                   f"({object_processing_duration/len(objects):.3f}s per object)")

        # Note: The old sequential processing loop has been replaced with parallel processing above

        out_json = save_root / f"{ts}.json"
        from .integrate_json import write_augmented_json
        write_augmented_json(str(paths['json']), str(out_json), per_object_aug)

        # Save per-camera images for this ts
        if args.viz_cam and drawn_images:
            for cam_name, img in drawn_images.items():
                out_img = save_root / 'viz' / cam_name / f"{ts}.jpg"
                save_image(img, str(out_img))
            # optional mosaic across cameras
            imgs_in_order = [drawn_images[k] for k in sorted(drawn_images.keys())]
            mosaic = make_mosaic(imgs_in_order, cols=4)
            if mosaic is not None:
                save_image(mosaic, str(save_root / 'viz' / f"{ts}_mosaic.jpg"))

    print(f"Done: outputs saved to {save_root}")

if __name__ == '__main__':
    main()
