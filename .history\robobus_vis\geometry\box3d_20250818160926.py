import numpy as np
import logging

logger = logging.getLogger(__name__)


def box_corners(center, l, w, h, yaw):
    """Return 8 corners of an oriented 3D box in base frame.
    l: length (x), w: width (y), h: height (z), yaw around +z.
    center: (x,y,z)
    """
    cx, cy, cz = center
    # local corners around origin
    x = l / 2.0
    y = w / 2.0
    z = h / 2.0
    corners = np.array([
        [ x,  y,  z], [ x, -y,  z], [-x, -y,  z], [-x,  y,  z],
        [ x,  y, -z], [ x, -y, -z], [-x, -y, -z], [-x,  y, -z]
    ], dtype=float)
    c, s = np.cos(yaw), np.sin(yaw)
    Rz = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=float)
    corners = (Rz @ corners.T).T
    corners += np.array([cx, cy, cz])
    return corners


def sample_box_surface(center, l, w, h, yaw, samples_per_face=1000):
    """Uniform-ish sampling on the 6 faces of the box.
    Returns Nx3 points in base frame.
    """
    cx, cy, cz = center
    # define 6 faces in local frame (z-up)
    faces = []
    # +x, -x faces
    faces.append((np.array([1,0,0]), w, h))
    faces.append((np.array([-1,0,0]), w, h))
    # +y, -y faces
    faces.append((np.array([0,1,0]), l, h))
    faces.append((np.array([0,-1,0]), l, h))
    # +z, -z faces
    faces.append((np.array([0,0,1]), l, w))
    faces.append((np.array([0,0,-1]), l, w))

    pts = []
    c, s = np.cos(yaw), np.sin(yaw)
    Rz = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=float)

    for normal, a, b in faces:
        # Construct local axes on the face plane
        if normal[0] != 0:
            u_dir = np.array([0,1,0])
            v_dir = np.array([0,0,1])
            offset = np.array([np.sign(normal[0]) * (l/2), 0, 0])
        elif normal[1] != 0:
            u_dir = np.array([1,0,0])
            v_dir = np.array([0,0,1])
            offset = np.array([0, np.sign(normal[1]) * (w/2), 0])
        else:
            u_dir = np.array([1,0,0])
            v_dir = np.array([0,1,0])
            offset = np.array([0, 0, np.sign(normal[2]) * (h/2)])
        # Stratified grid sampling
        n = int(np.sqrt(samples_per_face))
        if n < 1:
            n = 1
        for i in range(n):
            for j in range(n):
                u = (i + 0.5) / n - 0.5
                v = (j + 0.5) / n - 0.5
                p_local = offset + u * a * u_dir + v * b * v_dir
                p_world = (Rz @ p_local) + np.array([cx, cy, cz])
                pts.append(p_world)
    return np.array(pts, dtype=float)


def sample_box_surface_adaptive(center, l, w, h, yaw, cam_pos, target_samples=4800, min_samples_per_face=100):
    """
    Adaptive surface sampling that prioritizes camera-facing surfaces.

    ENHANCEMENT: Replaces uniform sampling with adaptive approach that:
    1. Calculates face visibility based on camera viewing direction
    2. Distributes samples proportionally to face visibility
    3. Ensures minimum sampling for all faces to maintain robustness

    Args:
        center: (cx, cy, cz) object center
        l, w, h: object dimensions (length, width, height)
        yaw: object rotation around z-axis
        cam_pos: (x, y, z) camera position in world coordinates
        target_samples: total number of samples to generate
        min_samples_per_face: minimum samples per face for robustness

    Returns:
        Nx3 array of surface points in world coordinates
    """
    cx, cy, cz = center

    # Calculate rotation matrix
    c, s = np.cos(yaw), np.sin(yaw)
    Rz = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=float)

    # Define face normals in local coordinates
    face_normals_local = np.array([
        [1, 0, 0],   # +x face
        [-1, 0, 0],  # -x face
        [0, 1, 0],   # +y face
        [0, -1, 0],  # -y face
        [0, 0, 1],   # +z face
        [0, 0, -1]   # -z face
    ])

    # Transform normals to world coordinates
    face_normals_world = (Rz @ face_normals_local.T).T

    # Calculate viewing direction from camera to object center
    view_dir = np.array([cx - cam_pos[0], cy - cam_pos[1], cz - cam_pos[2]])
    view_distance = np.linalg.norm(view_dir)
    if view_distance > 0:
        view_dir = view_dir / view_distance
    else:
        # Camera is at object center, use uniform sampling
        logger.warning("Camera at object center, falling back to uniform sampling")
        return sample_box_surface(center, l, w, h, yaw, samples_per_face=target_samples//6)

    # Calculate face visibility weights (dot product with viewing direction)
    face_weights = np.maximum(0, np.dot(face_normals_world, view_dir))

    # Normalize weights and ensure minimum sampling for all faces
    total_weight = face_weights.sum()
    if total_weight > 0:
        face_weights = face_weights / total_weight
    else:
        # All faces are back-facing, use uniform distribution
        face_weights = np.ones(6) / 6

    # Distribute samples: reserve minimum for each face, distribute remainder by weight
    reserved_samples = 6 * min_samples_per_face
    remaining_samples = max(0, target_samples - reserved_samples)

    samples_per_face = (face_weights * remaining_samples).astype(int) + min_samples_per_face

    # Adjust if we exceed target due to rounding
    total_assigned = samples_per_face.sum()
    if total_assigned > target_samples:
        # Remove excess from faces with highest sample counts
        excess = total_assigned - target_samples
        sorted_indices = np.argsort(samples_per_face)[::-1]
        for i in range(excess):
            face_idx = sorted_indices[i % len(sorted_indices)]
            if samples_per_face[face_idx] > min_samples_per_face:
                samples_per_face[face_idx] -= 1

    logger.debug(f"Adaptive sampling: {samples_per_face} samples per face, total: {samples_per_face.sum()}")

    # Generate samples for each face
    pts = []
    face_configs = [
        (np.array([1,0,0]), w, h, np.array([l/2, 0, 0])),    # +x face
        (np.array([-1,0,0]), w, h, np.array([-l/2, 0, 0])),  # -x face
        (np.array([0,1,0]), l, h, np.array([0, w/2, 0])),    # +y face
        (np.array([0,-1,0]), l, h, np.array([0, -w/2, 0])),  # -y face
        (np.array([0,0,1]), l, w, np.array([0, 0, h/2])),    # +z face
        (np.array([0,0,-1]), l, w, np.array([0, 0, -h/2]))   # -z face
    ]

    for i, (normal, a, b, offset) in enumerate(face_configs):
        n_samples = samples_per_face[i]
        if n_samples <= 0:
            continue

        # Generate stratified samples for this face
        n = int(np.sqrt(n_samples))
        if n < 1:
            n = 1

        # Construct local axes on the face plane
        if normal[0] != 0:
            u_dir, v_dir = np.array([0,1,0]), np.array([0,0,1])
        elif normal[1] != 0:
            u_dir, v_dir = np.array([1,0,0]), np.array([0,0,1])
        else:
            u_dir, v_dir = np.array([1,0,0]), np.array([0,1,0])

        samples_generated = 0
        for j in range(n):
            for k in range(n):
                if samples_generated >= n_samples:
                    break
                u = (j + 0.5) / n - 0.5
                v = (k + 0.5) / n - 0.5
                p_local = offset + u * a * u_dir + v * b * v_dir
                p_world = (Rz @ p_local) + np.array([cx, cy, cz])
                pts.append(p_world)
                samples_generated += 1
            if samples_generated >= n_samples:
                break

    return np.array(pts, dtype=float) if pts else np.array([], dtype=float).reshape(0, 3)
